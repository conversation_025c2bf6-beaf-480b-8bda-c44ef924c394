<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Document</title>

    <style>
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 0 auto;
        }

        table.content-table {
            width: 750px;
            margin: 0 auto;
        }

        table,
        th,
        td {
            border: 1px solid black;
        }

        th,
        td {
            padding: 6px;
            text-align: center;
            font-size: 10.5px;
        }

        table td.nama-pesertadidik {
            word-wrap: break-word !important;
            /* Membungkus kata yang panjang */
            word-break: break-all !important;
            /* Memecah kata di mana saja jika diperlukan */
            white-space: normal !important;
            /* Mengizinkan teks untuk membungkus */
            max-width: 150px !important;
            width: 150px !important;
            /* Mengatur lebar maksimum kolom */
            overflow-wrap: break-word !important;
            /* Membungkus kata yang panjang */
        }

        table th.nama-pesertadidik {
            word-wrap: break-word !important;
            /* Membungkus kata yang panjang */
            word-break: break-all !important;
            /* Memecah kata di mana saja jika diperlukan */
            white-space: normal !important;
            /* Mengizinkan teks untuk membungkus */
            max-width: 150px !important;
            width: 150px !important;
            /* Mengatur lebar maksimum kolom */
            overflow-wrap: break-word !important;
            /* Membungkus kata yang panjang */
        }



        th {
            background-color: #007BFF;
            color: white;
        }

        .header-table {
            text-align: center;
            border-bottom: 2px solid #000;
            padding-bottom: 10px;
            margin-bottom: 20px;
            width: 750px;
            margin: 0 auto 20px auto;
        }

        .header-table table {
            width: 100%;
            border: none;
        }

        .header-table td {
            border: none;
            padding: 5px;
            vertical-align: middle;
        }

        .content-table th {
            background-color: #007BFF;
            color: white;
        }

        .content-table td {
            color: #1D2939;
            font-size: 12px;
        }

        .bg-primary {
            background-color: #007BFF;
            color: white;
        }

        .bg-warning {
            background-color: #FFC107;
            color: white;
        }

        .bg-success {
            background-color: #28A745;
            color: white;
        }

        .bg-danger {
            background-color: #DC3545;
            color: white;
        }
    </style>
</head>

<body>
    <div class="header-table">
        <table>
            <tr>
                <td style="width: 100px; text-align: center;">
                    <?php if ($school->logo == null): ?>
                        <img src="<?= convertImageToBase64('./assets/smartschool-project.jpg') ?>" alt="Logo Sekolah" width="80">
                    <?php else: ?>
                        <img src="<?= convertImageToBase64('./uploads/logo/' . $school->logo) ?>" alt="Logo Sekolah" width="80">
                    <?php endif; ?>
                </td>

                <td style="width: 500px; text-align: center;">
                    <h2 style="font-size: 18px; margin: 0;"><?= strtoupper($school->name) ?></h2>
                    <p style="font-size: 12px; margin: 0;"><?= $school->address ?? null ?></p>
                    <p style="font-size: 12px; margin: 0;">Telepon: <?= $school->phonenumber ?? '-' ?> | Email: <?= $school->schoolemail ?? '-' ?></p>
                </td>

                <td style="width: 100px; text-align: center;">
                    <?php if ($school->logo_institution == null): ?>
                        <img src="<?= convertImageToBase64('./assets/smartschool-project.jpg') ?>" alt="Logo Sekolah" width="80">
                    <?php else: ?>
                        <img src="<?= convertImageToBase64('./uploads/logo/' . $school->logo_institution) ?>" alt="Logo Lembaga Pendidikan" width="80">
                    <?php endif; ?>
                </td>
            </tr>
        </table>
    </div>

    <div style="width: 100%; margin-bottom: 20px;">
        <div style="text-align: center;">
            <?php if (!empty($student) && isset($class)) : ?>
                <h2 style="font-size: 14px; color: #000A12;">LAPORAN PRESTASI SISWA <?= strtoupper($student->name)  ?> BULAN <?= strtoupper(bulan_indo(date('F', strtotime($month))) . ' ' . date('Y', strtotime($month))) ?></h2>
            <?php else: ?>
                <h2 style="font-size: 14px; color: #000A12;">LAPORAN PRESTASI SELURUH SISWA <?= strtoupper($class->name) ?> BULAN <?= strtoupper(bulan_indo(date('F', strtotime($month))) . ' ' . date('Y', strtotime($month))) ?></h2>
            <?php endif; ?>
        </div>

        <table class="table table-bordered content-table">
            <?php if (!empty($student)): ?>
                <!-- LAPORAN PER SISWA -->
                <thead>
                    <tr>
                        <th style="width: 30px;">NO</th>
                        <th style="width: 100px;">TANGGAL</th>
                        <th style="width: 450px;">DESKRIPSI</th>
                        <th style="width: 75px;">POIN</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($achievement)): ?>
                        <?php foreach ($achievement as $key => $value): ?>
                            <tr>
                                <td style="width: 30px;"><?= $key + 1 ?></td>
                                <td style="width: 100px;"><?= tgl_indo($value->date) ?></td>
                                <td style="width: 450px;"><?= nl2br(wordwrap($value->description, 80, "\n", true)) ?></td>
                                <td style="width: 75px;"><?= $value->achievementpoint ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="4" style="text-align: center;">Tidak ada laporan prestasi siswa</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            <?php else: ?>
                <!-- LAPORAN PER KELAS -->
                <thead>
                    <tr>
                        <th style="width: 60px;">NO</th>
                        <th style="width: 90px;">NIS</th>
                        <th style="width: 150px;">NAMA SISWA</th>
                        <th style="width: 135px;">TANGGAL</th>
                        <th style="width: 240px;">DESKRIPSI</th>
                        <th style="width: 75px;">POIN</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($achievement)): ?>
                        <?php foreach ($achievement as $key => $value): ?>
                            <tr>
                                <td style="width: 60px;"><?= $key + 1 ?></td>
                                <td style="width: 90px;"><?= $value->nis ?? '-' ?></td>
                                <td style="width: 150px;" class="nama-pesertadidik"><?= strtoupper($value->studentname) ?></td>
                                <td style="width: 135px;"><?= tgl_indo($value->date) ?></td>
                                <td style="width: 240px;"><?= nl2br(wordwrap($value->description, 60, "\n", true)) ?></td>
                                <td style="width: 75px;"><?= $value->achievementpoint ?></td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="6" style="text-align: center;">Tidak ada laporan prestasi siswa</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            <?php endif; ?>
        </table>
    </div>
</body>

</html>