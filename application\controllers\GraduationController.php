<?php
defined('BASEPATH') or die('No direct script access allowed!');

/**
 * @property MsClass $msclass
 * @property MsStudent $msstudent
 * @property MsGraduation $msgraduation
 * @property CI_DB_query_builder $db
 * @property Datatables $datatables
 */
class GraduationController extends MY_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsClass', 'msclass');
        $this->load->model('MsStudent', 'msstudent');
        $this->load->model('MsGraduation', 'msgraduation');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Alumni';
        $data['content'] = 'admin/master/graduation/index';
        $data['amountgraduation'] = $this->msstudent->get(array(
            'graduation_year' => date('Y'),
            'createdby' => getCurrentIdUser()
        ))->num_rows();

        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin()) {
            return JSONResponse();
        }

        $datatable = $this->datatables->make('MsStudent', 'QueryDatatables_Graduation', 'SearchDatatables_Graduation');

        $where = array();
        $where['b.createdby'] = getCurrentIdUser();

        $data = array();

        foreach ($datatable->getData($where) as $key => $value) {
            if ($value->graduation_year == null || $value->graduation_year == 0) continue;
            $detail = array();
            $actions = "";

            if ($value->graduationkey == null) {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('master/graduation/edit/' . $value->graduation_year) . "\" class=\"btn btn-primary btn-sm mb-2 me-1\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                        <i class=\"ti ti-edit\"></i>
                    </a>

                    <button type=\"button\" class=\"btn btn-danger btn-sm mb-2 me-1\" onclick=\"deleteGraduation('" . $value->graduation_year . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                        <i class=\"ti ti-trash\"></i>
                    </button>

                    <button type=\"button\" class=\"btn btn-success btn-sm mb-2\" onclick=\"keyGraduation('" . $value->graduation_year . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-success\" data-bs-original-title=\"Kunci\">
                        <i class=\"ti ti-key\"></i>
                    </button>
                </div>";
            } else {
                $actions .= "<div class=\"d-flex\">
                    <a href=\"" . base_url('master/graduation/detail/' . $value->graduation_year) . "\" class=\"btn btn-primary btn-sm\">
                        <i class=\"ti ti-users\"></i>
                    </a>
                </div>";
            }

            $detail[] = $value->graduation_year;
            $detail[] = $actions;

            $data[] = $detail;
        }

        return $datatable->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Tambah Alumni';
        $data['content'] = 'admin/master/graduation/add';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array(
                'createdby' => getCurrentIdUser(),
                'isdeleted' => null
            ))
            ->order_by('roman_to_int(a.level),a.name', 'asc')
            ->get()
            ->result();

        return $this->load->view('master', $data);
    }

    public function process_add()
    {
        try {
            $this->db->trans_begin();

            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $studentid = getPost('studentid');

            if (count($studentid) == 0) {
                throw new Exception('Pilih peserta didik terlebih dahulu minimal 1');
            }

            foreach ($studentid as $key => $value) {
                $cek = $this->msstudent->get(array('id' => $value));

                if ($cek->num_rows() == 0) {
                    continue;
                } else {
                    $update = array();
                    $update['graduation_year'] = date('Y');
                    $update['status'] = 'Graduated';
                    $update['updateddate'] = getCurrentDate();
                    $update['updatedby'] = getCurrentIdUser();

                    $update = $this->msstudent->update(array('id' => $value), $update);
                }
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal diubah');
            }

            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        } catch (Exception $e) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function edit($year)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msstudent->select('a.classid')
            ->where(array('graduation_year' => $year, 'createdby' => getCurrentIdUser()))
            ->group_by('a.classid')
            ->get();

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/graduation'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Ubah Alumni';
        $data['content'] = 'admin/master/graduation/edit';
        $data['class'] = $this->msclass->select('id,name,level')
            ->where(array('createdby' => getCurrentIdUser()))
            ->order_by('roman_to_int(a.level),a.name', 'asc')
            ->get()
            ->result();
        $data['selectedclass'] = $cek->result();
        $data['student'] = $this->msstudent->select('a.id,a.name as studentname,a.status ,b.name as classname,b.level')
            ->join('msclass b', 'a.classid = b.id')
            ->where_in('a.classid', array_column($data['selectedclass'], 'classid'))
            ->where(array(
                'a.createdby' => getCurrentIdUser(),
                'a.graduation_year' => $year,
            ))
            ->order_by('roman_to_int(b.level),b.name,a.name', 'asc')
            ->get();

        $getselectedstudent = $this->msstudent->get(array(
            'graduation_year' => $year,
            'createdby' => getCurrentIdUser(),
            'status' => 'Graduated'
        ))->num_rows();

        $data['selectedall'] = $getselectedstudent == $data['student']->num_rows() ? 'checked' : null;

        return $this->load->view('master', $data);
    }

    public function process_edit($year)
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $this->db->trans_begin();

        $cek = $this->msstudent->get(array('graduation_year' => $year, 'createdby' => getCurrentIdUser()));

        if ($cek->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $studentid = getPost('studentid', array());

        if (count($studentid) == 0) {
            return JSONResponseDefault('FAILED', 'Pilih peserta didik terlebih dahulu minimal 1');
        }

        $getstudentunselected = $this->msstudent->select('id')
            ->where_not_in('id', $studentid)
            ->where(array(
                'status' => 'Graduated',
                'createdby' => getCurrentIdUser()
            ))
            ->get()
            ->result();

        foreach ($getstudentunselected as $key => $value) {
            $update = array();
            $update['graduation_year'] = null;
            $update['status'] = 'Active';
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msstudent->update(array('id' => $value->id), $update);
        }

        foreach ($studentid as $key => $value) {
            $cek = $this->msstudent->get(array('id' => $value));

            if ($cek->num_rows() == 0) {
                continue;
            } else {
                $update = array();
                $update['graduation_year'] = date('Y');
                $update['status'] = 'Graduated';
                $update['rfidcode'] = null;
                $update['updateddate'] = getCurrentDate();
                $update['updatedby'] = getCurrentIdUser();

                $update = $this->msstudent->update(array('id' => $value), $update);
            }
        }

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Data gagal diubah');
        } else {
            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil diubah');
        }
    }

    public function process_delete()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $this->db->trans_begin();

        $year = getPost('year');

        $get = $this->msstudent->get(array(
            'status' => 'Graduated',
            'graduation_year' => $year,
            'createdby' => getCurrentIdUser()
        ));

        if ($get->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $this->msstudent->update(array('graduation_year' => $year, 'createdby' => getCurrentIdUser()), array('graduation_year' => null, 'status' => 'Active', 'rfidcode' => null));
        $this->msgraduation->delete(array('year' => $year, 'createdby' => getCurrentIdUser()));

        if ($this->db->trans_status() === FALSE) {
            $this->db->trans_rollback();

            return JSONResponseDefault('FAILED', 'Data gagal dihapus');
        } else {
            $this->db->trans_commit();

            return JSONResponseDefault('OK', 'Data berhasil dihapus, peserta didik kembali aktif');
        }
    }

    public function student()
    {
        if (!isLogin() && !isAdmin()) {
            return;
        } else {
            $classid = getPost('classid', array(0));
            $action = getPost('action', 'ADD');

            $graduation = $this->msgraduation->get(array(
                'createdby' => getCurrentIdUser()
            ));

            $wherein = array();
            foreach ($graduation->result() as $key => $value) {
                $wherein[] = $value->year;
            }

            if ($action == 'ADD') {
                $data = array();
                $data['student'] = $this->msstudent->select('a.id,a.status,a.name as studentname ,b.name as classname,b.level')
                    ->join('msclass b', 'a.classid = b.id')
                    ->where_in('a.classid', $classid)
                    ->where("(a.graduation_year IS NULL OR !FIND_IN_SET(a.graduation_year, '" . implode(',', $wherein) . "')) =", TRUE)
                    ->where(array(
                        "a.isdeleted" => null,
                        "a.status" => 'Active',
                        'a.createdby' => getCurrentIdUser()
                    ))
                    ->order_by('b.name,a.name', 'ASC')
                    ->get();
            } else {
                $data['student'] = $this->msstudent->select('a.id,a.status,a.name as studentname ,b.name as classname,b.level')
                    ->join('msclass b', 'a.classid = b.id')
                    ->where_in('a.classid', $classid)
                    ->where("(a.graduation_year IS NULL OR !FIND_IN_SET(a.graduation_year, '" . implode(',', $wherein) . "')) =", TRUE)
                    ->where(array(
                        'a.isdeleted' => null,
                        'a.createdby' => getCurrentIdUser()
                    ))
                    ->order_by('b.name,a.name', 'ASC')
                    ->get();
            }

            echo $this->load->view('admin/master/graduation/student', $data, true);
        }
    }

    public function process_key()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponseDefault('FAILED', 'Anda tidak memiliki akses');
        }

        $year = getPost('year');

        $cek = $this->msgraduation->get(array(
            'year' => $year,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek->num_rows() > 0) {
            return JSONResponseDefault('FAILED', 'Data telah dikunci');
        }

        $cekstudent = $this->msstudent->get(array(
            'graduation_year' => $year,
            'createdby' => getCurrentIdUser()
        ));

        if ($cekstudent->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data peserta didik tidak ditemukan');
        }

        $insert = array();
        $insert['year'] = $year;
        $insert['createddate'] = getCurrentDate();
        $insert['createdby'] = getCurrentIdUser();

        $insert = $this->msgraduation->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data berhasil dikunci');
        } else {
            return JSONResponseDefault('FAILED', 'Data gagal dikunci');
        }
    }

    public function detail($year)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $cek = $this->msgraduation->get(array(
            'year' => $year,
            'createdby' => getCurrentIdUser()
        ));

        if ($cek->num_rows() == 0) {
            return redirect(base_url('master/graduation'));
        }

        $data = array();
        $data['title'] = 'Manajemen - Detail Alumni';
        $data['content'] = 'admin/master/graduation/detail';
        $data['student'] = $this->msstudent->select('a.id,a.nis,a.name as studentname,a.rfidcode,a.status,a.phonenumber')
            ->where(array(
                'a.graduation_year' => $year,
                'a.createdby' => getCurrentIdUser(),
                'a.status' => 'Graduated',
                'a.isdeleted' => null
            ))
            ->order_by('a.name', 'ASC')
            ->get()
            ->result();
        $data['year'] = $year;

        return $this->load->view('master', $data);
    }

    public function delete()
    {
        try {
            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $this->db->trans_begin();

            $year = getPost('year');

            if (empty($year)) {
                throw new Exception('Tahun kelulusan tidak boleh kosong');
            }

            // Check if graduation data exists for this user
            $cek = $this->msstudent->get(array(
                'graduation_year' => $year,
                'createdby' => getCurrentIdUser(),
                'status' => 'Graduated'
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data alumni tidak ditemukan');
            }

            // Update all graduated students back to active status
            $update = array();
            $update['graduation_year'] = null;
            $update['status'] = 'Active';
            $update['updateddate'] = getCurrentDate();
            $update['updatedby'] = getCurrentIdUser();

            $this->msstudent->update(array(
                'graduation_year' => $year,
                'createdby' => getCurrentIdUser(),
                'status' => 'Graduated'
            ), $update);

            // Delete graduation record if exists
            $this->msgraduation->delete(array(
                'year' => $year,
                'createdby' => getCurrentIdUser()
            ));

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dihapus');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data alumni berhasil dihapus');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }

    public function key()
    {
        try {
            if (!isLogin() || !isAdmin()) {
                throw new Exception('Anda tidak memiliki akses');
            }

            $this->db->trans_begin();

            $year = getPost('year');

            if (empty($year)) {
                throw new Exception('Tahun kelulusan tidak boleh kosong');
            }

            // Check if graduation data exists for this user
            $cek = $this->msstudent->get(array(
                'graduation_year' => $year,
                'createdby' => getCurrentIdUser(),
                'status' => 'Graduated'
            ));

            if ($cek->num_rows() == 0) {
                throw new Exception('Data alumni tidak ditemukan');
            }

            // Check if graduation record already exists
            $graduationExists = $this->msgraduation->get(array(
                'year' => $year,
                'createdby' => getCurrentIdUser()
            ));

            if ($graduationExists->num_rows() == 0) {
                // Create graduation record
                $insert = array();
                $insert['year'] = $year;
                $insert['graduationkey'] = generateRandomString(32);
                $insert['createddate'] = getCurrentDate();
                $insert['createdby'] = getCurrentIdUser();

                $this->msgraduation->insert($insert);
            } else {
                // Update existing graduation record
                $update = array();
                $update['graduationkey'] = generateRandomString(32);
                $update['updateddate'] = getCurrentDate();
                $update['updatedby'] = getCurrentIdUser();

                $this->msgraduation->update(array(
                    'year' => $year,
                    'createdby' => getCurrentIdUser()
                ), $update);
            }

            if ($this->db->trans_status() === FALSE) {
                throw new Exception('Data gagal dikunci');
            }

            $this->db->trans_commit();
            return JSONResponseDefault('OK', 'Data alumni berhasil dikunci');
        } catch (Exception $e) {
            $this->db->trans_rollback();
            return JSONResponseDefault('FAILED', $e->getMessage());
        }
    }
}
