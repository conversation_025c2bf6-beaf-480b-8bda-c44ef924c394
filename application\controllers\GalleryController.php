<?php
defined('BASEPATH') or exit('No direct script access allowed');

/**
 * @property MsGalleryS $msgallerys
 * @property Datatables $datatables
 */
class GalleryController extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();

        $this->load->model('MsGallerys', 'msgallerys');
    }

    public function index()
    {
        if (!isLogin()) {
            return redirect('auth/login/admin');
        }

        $data = array();
        $data['title'] = 'Data Sekolah - Galeri';
        $data['content'] = 'admin/master/gallery/index';
        return $this->load->view('master', $data);
    }

    public function datatables()
    {
        if (!isLogin() || !isAdmin()) {
            return JSONResponse();
        }

        $datatables = $this->datatables->make('MsGallerys', 'QueryDatatables', 'SearchDatatables');

        $where = array(
            'a.createdby' => getCurrentIdUser()
        );

        $data = array();

        foreach ($datatables->getData($where) as $key => $value) {
            $detail = array();

            if (strtolower(trim($value->category)) === 'video') {
                $detail[] = '<a href="' . $value->link . '" target="_blank">Link Video</a>';
            } else {
                $detail[] = '<img src="' . base_url('uploads/gallery/' . $value->picture) . '" style="width: 100px; height: auto;">';
            }

            $actions = "<div class=\"d-flex\">
                <a href=\"" . base_url('master/gallery/edit/' . $value->id) . "\" class=\"btn btn-primary btn-sm ms-2\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-primary\" data-bs-original-title=\"Ubah\">
                    <i class=\"ti ti-edit\"></i>
                </a>

                   <button type=\"button\" class=\"btn btn-danger btn-sm ms-2\" onclick=\"deleteGallery('" . $value->id . "')\" data-bs-toggle=\"tooltip\" data-bs-placement=\"top\" data-bs-custom-class=\"tooltip-danger\" data-bs-original-title=\"Hapus\">
                    <i class=\"ti ti-trash\"></i>
                </button>
            </div>";

            $detail[] = $actions;
            $data[] = $detail;
        }

        return $datatables->json($data);
    }

    public function add()
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $data = array();
        $data['title'] = 'Data Sekolah - Tambah Galeri';
        $data['content'] = 'admin/master/gallery/add';

        $this->load->view('master', $data);
    }

    public function process_add()
    {
        if (!isLogin() || !isAdmin()) {
            throw new Exception('Anda tidak memiliki akses');
        }

        $kategori = getPost('kategori_id');
        $picture_path = null;
        $link_video = null;

        if ($kategori === 'foto') {
            if (isset($_FILES['gambar']) && $_FILES['gambar']['size'] > 0) {
                $config['upload_path']   = './uploads/gallery/';
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['max_size']      = 2048; // 2MB
                $config['encrypt_name']  = true;

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('gambar')) {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }

                $upload_data  = $this->upload->data();
                $picture_path = $upload_data['file_name'];
            } else {
                return JSONResponseDefault('FAILED', 'File gambar tidak boleh kosong');
            }
        } elseif ($kategori === 'video') {
            $link_video = getPost('link', '');

            if (empty(trim($link_video))) {
                return JSONResponseDefault('FAILED', 'Link video tidak boleh kosong');
            }
        } else {
            return JSONResponseDefault('FAILED', 'Kategori tidak valid.');
        }

        $insert = array(
            'category'     => $kategori,
            'picture'      => $picture_path, // ini hanya untuk foto
            'link'         => $link_video,   // ini hanya untuk video
            'createddate'  => getCurrentDate(),
            'createdby'    => getCurrentIdUser()
        );

        $insert = $this->msgallerys->insert($insert);

        if ($insert) {
            return JSONResponseDefault('OK', 'Data Galeri berhasil ditambahkan');
        } else {
            return JSONResponseDefault('FAILED', 'Data Galeri gagal ditambahkan');
        }
    }

    public function edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            return redirect(base_url('auth/login/admin'));
        }

        $gallery = $this->msgallerys->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($gallery->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }
        $data = array();
        $data['galeri'] = $gallery->row();
        $data['title'] = 'Data Sekolah - Ubah Galeri';
        $data['content'] = 'admin/master/gallery/edit';

        $this->load->view('master', $data);
    }

    public function process_edit($id)
    {
        if (!isLogin() || !isAdmin()) {
            throw new Exception('Anda tidak memiliki akses');
        }

        // Ambil data galeri berdasarkan ID
        $query = $this->msgallerys->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($query->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Galeri tidak ditemukan');
        }

        $galeri = $query->row();  // <-- ambil satu row data

        $categoryid = getPost('kategori_id');
        $picture_path = $galeri->picture; // sekarang sudah valid
        $link_video = null;

        if ($categoryid === 'video') {
            $link_video = getPost('link', '');
            if (empty(trim($link_video))) {
                return JSONResponseDefault('FAILED', 'Link video tidak boleh kosong ');
            }
            $picture_path = null; // Tidak pakai gambar untuk video
        } else {
            $picture = $_FILES['gambar'];

            if ($picture['size'] > 0) {
                if (!empty($galeri->picture) && file_exists('./uploads/gallery/' . $galeri->picture)) {
                    unlink('./uploads/gallery/' . $galeri->picture);
                }

                $config['upload_path'] = './uploads/gallery/';
                $config['allowed_types'] = 'jpg|jpeg|png';
                $config['max_size'] = 2048;
                $config['encrypt_name'] = TRUE;

                $this->load->library('upload', $config);

                if (!$this->upload->do_upload('gambar')) {
                    return JSONResponseDefault('FAILED', $this->upload->display_errors('', ''));
                }

                $upload_data = $this->upload->data();
                $picture_path = $upload_data['file_name'];
            }
        }

        $update = array(
            'category' => $categoryid,
            'picture' => $picture_path,
            'link' => $link_video,
            'updateddate' => getCurrentDate(),
            'updatedby' => getCurrentIdUser()
        );

        $update_result = $this->msgallerys->update(array('id' => $id), $update);

        if ($update_result) {
            return JSONResponseDefault('OK', 'Data Galeri berhasil diperbarui');
        } else {
            return JSONResponseDefault('FAILED', 'Data Galeri gagal diperbarui');
        }
    }

    public function delete()
    {
        $id = getPost('id');

        if (empty($id)) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $kategori = $this->msgallerys->get(array(
            'id' => $id,
            'createdby' => getCurrentIdUser()
        ));

        if ($kategori->num_rows() == 0) {
            return JSONResponseDefault('FAILED', 'Data tidak ditemukan');
        }

        $galeri = $kategori->row();

        // Hapus gambar jika ada dan kategori adalah foto
        if ($galeri->category === 'foto' && !empty($galeri->picture)) {
            $gambar_path = FCPATH . 'uploads/gallery/' . $galeri->picture;
            if (file_exists($gambar_path)) {
                unlink($gambar_path);
            }
        }

        $delete = $this->msgallerys->delete(array('id' => $id));

        if ($delete) {
            return JSONResponseDefault('OK', 'Data Galeri berhasil dihapus');
        } else {
            return JSONResponseDefault('FAILED', 'Data Galeri gagal dihapus');
        }
    }
}
