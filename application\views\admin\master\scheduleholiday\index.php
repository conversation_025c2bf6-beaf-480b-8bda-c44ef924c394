<?php
defined('BASEPATH') or exit('No direct script access allowed');
?>
<h4 class="py-3 mb-4">
    <span class="text-muted fw-light">Manajemen /</span> Jadwal Libur
</h4>
<div class="row">
    <div class="col-md-12">


        <!-- DataTable with Buttons -->
        <div class="card">
            <div class="card-header">
                <div class="row">
                    <div class="col-md-12">
                        <div class="d-flex align-items-center justify-content-between">
                            <h5>Data Jadwal Libur</h5>

                            <?php if (isAdmin()) : ?>
                                <div>
                                    <a href="<?= base_url(uri_string() . '/add') ?>" class="btn btn-primary">
                                        <i class="ti ti-plus"></i>
                                        Tambah Jadwal Libur
                                    </a>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                    <div class="col-md-3 mt-3">
                        <label for="month" class="form-label">Bulan</label>
                        <input type="month" name="month" id="month" class="form-control" onchange="filter()">
                    </div>

                    <div class="col-md-3 mt-3">
                        <label for="classid" class="form-label">Kelas</label>

                        <select name="classid" id="classid" class="form-control select2" onchange="filter()">
                            <option value="" selected disabled>Pilih salah satu</option>
                            <?php foreach ($class as $value) : ?>
                                <option value="<?= $value->id ?>"><?= ($value->level ?? null) != null ? $value->level . ' - ' . $value->name : $value->name ?></option>
                            <?php endforeach; ?>
                        </select>
                    </div>
                </div>
            </div>

            <div class="card-body">
                <div class="table-responsive">
                    <table class="table datatables-scheduleholiday">
                        <thead>
                            <th>Tipe</th>
                            <th>Tanggal Awal Libur</th>
                            <th>Tanggal Akhir Libur</th>
                            <th>Kelas</th>
                            <th>Alasan</th>
                            <?php if (isAdmin()) : ?>
                                <th>Aksi</th>
                            <?php endif; ?>
                        </thead>

                        <tbody>

                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>
<script>
    window.onload = function() {
        $('.datatables-scheduleholiday').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: false,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
            },
            initComplete: function() {
                $('[data-bs-toggle="tooltip"]').tooltip();
            }
        });
    }

    function deleteScheduleHoliday(id, reason) {
        Swal.fire({
            title: 'Apakah anda yakin?',
            text: `Anda akan menghapus jadwal libur ${reason}!`,
            icon: 'warning',
            showCancelButton: true,
            customClass: {
                confirmButton: "btn btn-danger waves-effect waves-light",
                cancelButton: "btn btn-label-secondary waves-effect"
            },
            buttonsStyling: !1,
            confirmButtonText: "Ya, hapus!",
            cancelButtonText: "Batal"
        }).then(function(result) {
            if (result.value) {
                setTimeout(function() {
                    $.ajax({
                        url: '<?= base_url(uri_string() . '/delete') ?>',
                        method: 'POST',
                        dataType: 'json',
                        data: {
                            id: id
                        },
                        success: function(response) {
                            if (response.RESULT == 'OK') {
                                return swalMessageSuccess(response.MESSAGE, (ok) => {
                                    $('.datatables-scheduleholiday').DataTable().ajax.reload();
                                });
                            } else {
                                return swalMessageFailed(response.MESSAGE);
                            }
                        }
                    }).fail(function() {
                        return swalError();
                    });
                }, 1000);
            }
        });
    }

    function filter() {

        $('.datatables-scheduleholiday').DataTable().destroy();
        $('.datatables-scheduleholiday').DataTable({
            responsive: true,
            processing: true,
            serverSide: true,
            stateSave: false,
            ordering: false,
            ajax: {
                url: '<?= base_url(uri_string() . '/datatables') ?>',
                type: 'POST',
                data: {
                    month: $('#month').val(),
                    classid: $('#classid').val()
                }
            }
        });
    }
</script>