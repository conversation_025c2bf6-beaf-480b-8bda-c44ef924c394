<?php
defined('BASEPATH') or die('No direct script access allowed!');

class MsLearningMaterial extends MY_Model
{
    protected $table = 'mslearningmaterial';

    public $SearchDatatables = array(
        'a.title',
        'a.type',
        'b.name',
        'c.name',
        'a.description'
    );

    public function QueryDatatables()
    {
        $this->db->select('a.document, a.id, a.thumbnail, a.title, a.type, a.description, a.link, b.name as coursename, CONCAT("<ul><li>", GROUP_CONCAT(IF(c.level IS NULL OR c.level = "", c.name, CONCAT(c.level, " - ", c.name)) ORDER BY c.level ASC SEPARATOR "</li><li>"), "</li></ul>") AS classname')
            ->from($this->table . ' a')
            ->join('mscourse b', 'a.courseid = b.id', 'left')
            ->join("(SELECT d.learningmaterialid, d.classid FROM mslearningmaterialdetail d GROUP BY d.learningmaterialid, d.classid) d", 'a.id = d.learningmaterialid', 'left')
            ->join('msclass c', 'd.classid = c.id', 'left')
            ->group_by('a.document, a.id, a.thumbnail, a.title, a.type, a.description, a.link, b.name')
            ->order_by('a.title', 'ASC');

        return $this;
    }
}
