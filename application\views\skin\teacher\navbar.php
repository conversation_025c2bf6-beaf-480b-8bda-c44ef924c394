<?php
defined('BASEPATH') or die('No direct script access allowed!');
?>
<div class="layout-menu-toggle navbar-nav align-items-xl-center me-3 me-xl-0   d-xl-none ">
    <a class="nav-item nav-link px-0 me-xl-4" href="javascript:void(0)">
        <i class="ti ti-menu-2 ti-sm"></i>
    </a>
</div>

<div class="navbar-nav-right d-flex align-items-center" id="navbar-collapse">
    <!-- Search -->
    <div class="navbar-nav align-items-center">
        <div class="nav-item navbar-search-wrapper mb-0">
            <a class="nav-item nav-link search-toggler d-flex align-items-center px-0" href="javascript:void(0);">
                <span class="d-none d-md-inline-block" style="font-size: 18px; font-weight: 600;"><?= $title ?? null ?></span>
            </a>
        </div>
    </div>
    <!-- /Search -->

    <ul class="navbar-nav flex-row align-items-center ms-auto">
        <li class="nav-item me-3">
            <select name="academicyear" id="academicyear" class="form-control" onchange="changeAcademicYear(this.value)">
                <?php foreach (getActiveAcademicYearDetail() as $key => $value): ?>
                    <option value="<?= $value->id ?>" <?= getSessionValue('ACADEMICYEARDETAILID') == $value->id ? 'selected' : null ?>>Semester <?= $value->semester ?> (<?= tgl_indo($value->startdate) ?> s/d <?= tgl_indo($value->enddate) ?>)</option>
                <?php endforeach; ?>
            </select>
        </li>

        <div class="nav-item me-3">
            <div id="clock" class="text-center"></div>
        </div>

        <!-- User -->
        <li class="nav-item navbar-dropdown dropdown-user dropdown">
            <a class="nav-link dropdown-toggle hide-arrow" href="javascript:void(0);" data-bs-toggle="dropdown">
                <div class="avatar avatar-online">
                    <img src="<?= base_url() ?>assets/img/avatars/1.png" alt class="h-auto rounded-circle">
                </div>
            </a>

            <ul class="dropdown-menu dropdown-menu-end">
                <li>
                    <a class="dropdown-item" href="javascript:void(0);">
                        <div class="d-flex">
                            <div class="flex-shrink-0 me-3">
                                <div class="avatar avatar-online">
                                    <img src="<?= base_url() ?>assets/img/avatars/1.png" alt class="h-auto rounded-circle">
                                </div>
                            </div>

                            <div class="flex-grow-1">
                                <span class="fw-medium d-block"><?= strtoupper(getSessionValue('NAME')) ?></span>

                                <small class="text-muted">
                                    GURU
                                </small>
                            </div>
                        </div>
                    </a>
                </li>

                <li>
                    <div class="dropdown-divider"></div>
                </li>

                <li>
                    <a class="dropdown-item" href="<?= base_url('auth/changepassword/teacher') ?>">
                        <i class="ti ti-key me-2 ti-sm"></i>
                        <span class="align-middle">Ubah Password</span>
                    </a>
                </li>

                <li>
                    <div class="dropdown-divider"></div>
                </li>

                <li>
                    <a class="dropdown-item" href="javascript:;" onclick="logout('<?= base_url('auth/logout/teacher') ?>')">
                        <i class="ti ti-logout me-2 ti-sm"></i>
                        <span class="align-middle">Keluar</span>
                    </a>
                </li>
            </ul>
        </li>
        <!--/ User -->
    </ul>
</div>