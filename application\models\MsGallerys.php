<?php
defined('BASEPATH') or exit('No direct script access allowed');

class MsGallerys extends MY_Model
{
    protected $table = 'msgallerys';

    public $SearchDatatables = array(
        "picture",
    );

    public function getAllGalleryData()
    {
        // Ambil semua data galeri
        $query = $this->db->get($this->table);

        return $query->result_array();  // Kembalikan hasil sebagai array
    }

    public function QueryDatatables()
    {
        $this->db->select('a.*')
            ->from($this->table . ' a')
            ->order_by('a.createddate', 'DESC');

        return $this;
    }
}
