<!DOCTYPE html>
<html lang="en" class="light-style layout-wide  customizer-hide" dir="ltr" data-theme="theme-default" data-assets-path="<?= base_url() ?>assets/" data-template="vertical-menu-template">

<head>
    <meta charset="utf-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

    <title>Login Page - Smart School Project Absensi</title>


    <meta name="description" content="Start your development with a Dashboard for Bootstrap 5" />
    <meta name="keywords" content="dashboard, bootstrap 5 dashboard, bootstrap 5 design, bootstrap 5">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="https://demos.pixinvent.com/vuexy-html-admin-template/assets/img/favicon/favicon.ico" />

    <!-- Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com/">
    <link rel="preconnect" href="https://fonts.gstatic.com/" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&amp;ampdisplay=swap" rel="stylesheet">

    <!-- Icons -->
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/fonts/fontawesome.css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/fonts/tabler-icons.css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/fonts/flag-icons.css" />

    <!-- Core CSS -->
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/css/rtl/core.css" class="template-customizer-core-css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/css/rtl/theme-default.css" class="template-customizer-theme-css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/css/demo.css" />

    <!-- Vendors CSS -->
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/libs/node-waves/node-waves.css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/libs/typeahead-js/typeahead.css" />
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/libs/sweetalert2/sweetalert2.css">
    <!-- Vendor -->
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/libs/%40form-validation/form-validation.css" />

    <!-- Page CSS -->
    <!-- Page -->
    <link rel="stylesheet" href="<?= base_url() ?>assets/vendor/css/pages/page-auth.css">

    <!-- Helpers -->
    <script src="<?= base_url() ?>assets/vendor/js/helpers.js"></script>
    <script src="<?= base_url() ?>assets/js/config.js"></script>

    <style>
        body {
            background-color: rgba(7, 178, 204, 0.1);
        }

        .btn-primary,
        .btn-primary:hover,
        .btn-check:checked+.btn-primary,
        .btn-check:active+.btn-primary,
        .btn-primary:active,
        .btn-primary.active,
        .btn-primary.show.dropdown-toggle,
        .show>.btn-primary.dropdown-toggle,
        .btn-primary.disabled,
        .btn-primary:disabled {
            background-color: #07b2cc !important;
            border-color: #07b2cc !important;
        }

        .form-control:focus,
        .form-select:focus,
        .input-group:focus-within .form-control,
        .input-group:focus-within .input-group-text {
            border-color: #07b2cc !important;
        }

        .btn-outline-white:hover {
            color: #07b2cc !important;
        }

        .authentication-wrapper.authentication-basic .authentication-inner:before,
        .authentication-wrapper.authentication-basic .authentication-inner:after {
            background: unset;
        }
    </style>
</head>

<body>
    <!-- Content -->

    <div class="container-xxl">
        <div class="authentication-wrapper authentication-basic container-p-y">
            <div class="authentication-inner py-4">
                <!-- Login -->
                <div class="card text-white" style="background-color: #07b2cc;">
                    <div class="card-body">
                        <h4 class="mb-1 pt-2 text-white fw-semibold">Portal Guru Smart School Project</h4>
                        <p class="mb-4">Silahkan masukkan akun anda untuk ke tahap selanjutnya</p>

                        <form id="frmLogin" class="mb-3" action="<?= base_url(uri_string() . '/process') ?>" method="POST" autocomplete="off">
                            <div class="mb-3">
                                <label for="nip" class="form-label text-white">NIP <span class="text-danger">*</span></label>
                                <input type="text" class="form-control" id="nip" name="nip" placeholder="Masukkan NIP" autofocus required>
                            </div>
                            <div class="mb-3 form-password-toggle">
                                <div class="d-flex justify-content-between">
                                    <label class="form-label text-white" for="password">Password <span class="text-danger">*</span></label>
                                </div>
                                <div class="input-group input-group-merge">
                                    <input type="password" id="password" class="form-control" name="password" placeholder="&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;&#xb7;" aria-describedby="password" required />
                                    <span class="input-group-text cursor-pointer"><i class="ti ti-eye-off"></i></span>
                                </div>
                            </div>
                            <div class="mb-3">
                                <button type="submit" class="btn btn-outline-white d-grid w-100 mb-2" type="submit">Masuk</button>
                                <a href="<?= base_url('landing') ?>" class="btn btn-danger w-100 mb-2">Kembali</a>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- / Content -->

    <!-- Core JS -->
    <script src="<?= base_url() ?>assets/vendor/libs/jquery/jquery.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/popper/popper.js"></script>
    <script src="<?= base_url() ?>assets/vendor/js/bootstrap.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/node-waves/node-waves.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/hammer/hammer.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/i18n/i18n.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/typeahead-js/typeahead.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/sweetalert2/sweetalert2.js"></script>
    <script src="<?= base_url() ?>assets/vendor/js/menu.js"></script>

    <!-- endbuild -->

    <!-- Vendors JS -->
    <script src="<?= base_url() ?>assets/vendor/libs/%40form-validation/popular.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/%40form-validation/bootstrap5.js"></script>
    <script src="<?= base_url() ?>assets/vendor/libs/%40form-validation/auto-focus.js"></script>

    <!-- Main JS -->
    <script src="<?= base_url() ?>assets/js/main.js"></script>

    <!-- Page JS -->
    <script src="<?= base_url() ?>assets/js/pages-auth.js"></script>
    <script src="<?= base_url() ?>assets/js/ajax-request.js"></script>
    <script src="<?= base_url() ?>assets/js/script.js?v=1.0.0"></script>

    <script>
        $.AjaxRequest('#frmLogin', {
            success: function(response) {
                if (response.RESULT == 'OK') {
                    return swalMessageSuccess(response.MESSAGE, (ok) => {
                        return window.location.reload();
                    });
                } else {
                    return swalMessageFailed(response.MESSAGE);
                }
            },
            error: function() {
                return swalError();
            }
        });
    </script>

</body>

</html>